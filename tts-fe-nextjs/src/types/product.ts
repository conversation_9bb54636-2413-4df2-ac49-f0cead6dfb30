export interface Product {
  id: number;
  idTT: string;
  title: string;
  description: string;
  category: string;
  categoryChains: Array<{
    id: string;
    isLeaf: boolean;
    parentId: string;
    localName: string;
  }>;
  brandInfo?: string;
  brand?: {
    id: string;
    name: string;
  };
  status: 'DRAFT' | 'PENDING' | 'AUDITING' | 'ACTIVATE' | 'FAILED' | 'FREEZE' | 'PLATFORM_DEACTIVATED' | 'SELLER_DEACTIVATED';
  productImages: string[];
  mainImages: Array<{
    uri: string;
    urls: string[];
    width: number;
    height: number;
    thumbUrls: string[];
  }>;
  packageDimensions?: {
    unit: string;
    width: string;
    height: string;
    length: string;
  };
  packageWeight?: {
    unit: string;
    value: string;
  };
  sizeChart?: {
    image?: {
      uri?: string;
      urls?: string[];
      width?: number;
      height?: number;
      thumbUrls?: string[];
    };
  };
  skus: Array<{
    id: number;
    idTT: string;
    sellerSku: string;
    inventory: Array<{
      quantity: number;
      warehouseId: string;
    }>;
    salesAttributes: Array<{
      id: string;
      name: string;
      valueId: string;
      valueName: string;
      skuImg?: {
        height?: number;
        width?: number;
        thumbUrls?: string[];
        uri?: string;
        urls?: string[];
      };
      supplementarySkuImages?: {
        height?: number;
        width?: number;
        thumbUrls?: string[];
        uri?: string;
        urls?: string[];
      }[];
    }>;
    price: {
      currency: string;
      salePrice: number;
      unitPrice?: number;
      taxExclusivePrice: number;
    };
  }>;
  tiktokShopId: number;
  tiktokShop?: {
    id: number;
    name: string;
    friendly_name?: string;
    region?: string;
    code: string;
  };
  createdAt: string;
  updatedAt: string;
}

export interface ProductCreateInput {
  name: string;
  slug: string;
  description?: string;
  price: number;
  salePrice?: number;
  sku: string;
  stock: number;
  categoryId: number;
  brandId?: number;
  isActive?: boolean;
  images: string[];
}

export type ProductUpdateInput = Partial<ProductCreateInput>;

export interface ProductFilters {
  page?: number;
  limit?: number;
  sortField?: string;
  sortDirection?: 'asc' | 'desc';
  search?: string;
  status?: string;
  categoryId?: number;
  brandId?: number;
  tiktokShopId?: number;
  createTimeFrom?: number;
  createTimeTo?: number;
}

export interface ProductListResponse {
  data: Product[];
  meta: {
    totalItems: number;
    totalPages: number;
    currentPage: number;
    limit: number;
  };
}

export interface ProductSyncDto {
  tiktokShopId: number;
  pageSize?: number;
  sortOrder?: 'ASC' | 'DESC';
  sortField?: string;
  pageToken?: string;
  maxPages?: number;
  forceFullSync?: boolean;
}

export interface ProductSyncMultiShopDto {
  tiktokShopIds: number[];
  pageSize?: number;
  sortOrder?: 'ASC' | 'DESC';
  sortField?: string;
  maxPages?: number;
  forceFullSync?: boolean;
}

export interface ProductSyncResponse {
  jobId: string | number;
  tiktokShopId: number;
  shopName: string;
  status: string;
}
