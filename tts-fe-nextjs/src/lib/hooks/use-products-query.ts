'use client';

import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';
import { useSession } from 'next-auth/react';
import { toast } from 'sonner';
import {
  getProducts,
  getProduct,
  synchronizeProducts,
  synchronizeProductsMultiShop,
  exportProducts
} from '@/lib/api/services/product-service';
import {
  ProductFilters,
  ProductSyncDto,
  ProductSyncMultiShopDto,
  ProductListResponse
} from '@/types/product';
import { createQueryKeys } from '@/lib/api/api-client';

// Create query keys for products
export const productKeys = createQueryKeys('products');

/**
 * Hook for fetching products with pagination and filtering
 */
export const useProducts = (filters: ProductFilters = {}) => {
  const { data: session, status } = useSession();
  const isAuthenticated = status === 'authenticated';

  return useQuery({
    queryKey: productKeys.list(filters),
    queryFn: () => getProducts(filters, session?.backendToken),
    enabled: isAuthenticated,
    staleTime: 5 * 60 * 1000, // 5 minutes
  });
};

/**
 * Hook for fetching a single product by ID
 */
export const useProduct = (id: number) => {
  const { data: session, status } = useSession();
  const isAuthenticated = status === 'authenticated';

  return useQuery({
    queryKey: productKeys.detail(id),
    queryFn: () => getProduct(id, session?.backendToken),
    enabled: isAuthenticated && !!id,
    staleTime: 5 * 60 * 1000, // 5 minutes
  });
};

/**
 * Hook for synchronizing products from TikTok Shop
 */
export const useSynchronizeProducts = () => {
  const { data: session } = useSession();
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: (data: ProductSyncDto) =>
      synchronizeProducts(data, session?.backendToken),
    onSuccess: (result, variables) => {
      toast.success(
        `Product synchronization job queued successfully! Job ID: ${result.jobId} for shop: ${result.shopName}`
      );

      // Invalidate products queries to refresh the data
      queryClient.invalidateQueries({
        queryKey: productKeys.lists()
      });
    },
    onError: (error: any) => {
      console.error('Failed to synchronize products:', error);
      // Error toast is already shown by API client interceptor
    },
  });
};

/**
 * Hook for synchronizing products from multiple TikTok Shops
 */
export const useSynchronizeProductsMultiShop = () => {
  const { data: session } = useSession();
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: (data: ProductSyncMultiShopDto) =>
      synchronizeProductsMultiShop(data, session?.backendToken),
    onSuccess: (result, variables) => {
      const jobCount = result.length;
      const shopNames = result.map(r => r.shopName).join(', ');
      toast.success(
        `Product synchronization jobs queued successfully! ${jobCount} jobs created for shops: ${shopNames}`
      );

      // Invalidate products queries to refresh the data
      queryClient.invalidateQueries({
        queryKey: productKeys.lists()
      });
    },
    onError: (error: any) => {
      console.error('Failed to synchronize products from multiple shops:', error);
      // Error toast is already shown by API client interceptor
    },
  });
};

/**
 * Alias for useSynchronizeProducts for backward compatibility
 */
export const useSyncProducts = useSynchronizeProducts;

/**
 * Hook for exporting products to CSV
 */
export const useExportProducts = () => {
  const { data: session } = useSession();

  return useMutation({
    mutationFn: (filters?: ProductFilters) =>
      exportProducts(filters, session?.backendToken),
    onSuccess: (blob) => {
      // Create download link
      const url = window.URL.createObjectURL(blob);
      const link = document.createElement('a');
      link.href = url;
      link.download = `products-export-${new Date().toISOString().split('T')[0]}.csv`;
      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);
      window.URL.revokeObjectURL(url);

      toast.success('Products exported successfully!');
    },
    onError: (error: any) => {
      console.error('Failed to export products:', error);
      // Error toast is already shown by API client interceptor
    },
  });
};
