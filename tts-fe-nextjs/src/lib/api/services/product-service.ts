'use client';

import { apiRequest } from '@/lib/api/api-client';
import {
  Product,
  ProductFilters,
  ProductSyncDto,
  ProductSyncMultiShopDto,
  ProductSyncResponse,
  ProductListResponse
} from '@/types/product';

/**
 * Fetch all products with pagination and filtering
 */
export const getProducts = async (
  params?: ProductFilters,
  token?: string
): Promise<ProductListResponse> => {
  return apiRequest<ProductListResponse>({
    url: '/products',
    method: 'GET',
    params,
    token
  });
};

/**
 * Fetch a single product by ID
 */
export const getProduct = async (
  id: number,
  token?: string
): Promise<Product> => {
  return apiRequest<Product>({
    url: `/products/${id}`,
    method: 'GET',
    token
  });
};

/**
 * Synchronize products from TikTok Shop
 */
export const synchronizeProducts = async (
  data: ProductSyncDto,
  token?: string
): Promise<ProductSyncResponse> => {
  return apiRequest<ProductSyncResponse>({
    url: '/products/synchronize',
    method: 'POST',
    data,
    token
  });
};

/**
 * Synchronize products from multiple TikTok Shops
 */
export const synchronizeProductsMultiShop = async (
  data: ProductSyncMultiShopDto,
  token?: string
): Promise<ProductSyncResponse[]> => {
  return apiRequest<ProductSyncResponse[]>({
    url: '/products/synchronize-multi-shop',
    method: 'POST',
    data,
    token
  });
};

/**
 * Synchronize product details from TikTok Shop
 */
export const synchronizeProductDetails = async (
  id: number,
  token?: string
): Promise<Product> => {
  return apiRequest<Product>({
    url: `/products/synchronize-detail/${id}`,
    method: 'POST',
    token
  });
};

/**
 * Export products to CSV
 */
export const exportProducts = async (
  filters?: ProductFilters,
  token?: string
): Promise<Blob> => {
  return apiRequest<Blob>({
    url: '/products/export',
    method: 'POST',
    data: filters,
    token,
    responseType: 'blob'
  });
};
