import { Injectable, Logger, NotFoundException } from '@nestjs/common';
import { Queue } from 'bull';
import { InjectQueue } from '@nestjs/bull';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository, In } from 'typeorm';
import { ProductSyncDto, ProductSyncMultiShopDto } from '../../products/dto/product-sync.dto';
import { TikTokShop } from '../../tiktok-shop/entities/tiktok-shop.entity';

@Injectable()
export class ProductSyncQueueService {
  private readonly logger = new Logger(ProductSyncQueueService.name);

  constructor(
    @InjectQueue('product-sync') private readonly productQueue: Queue,
    @InjectRepository(TikTokShop)
    private readonly tikTokShopRepository: Repository<TikTokShop>,
  ) {}

  /**
   * Add a product synchronization job to the queue
   * @param params Object containing syncDto and userId
   * @returns Job information
   */
  async addProductSyncJob(params: {
    syncDto: ProductSyncDto;
    userId: number;
  }): Promise<{
    jobId: string | number;
    tiktokShopId: number;
    shopName: string;
    status: string;
  }> {
    const { syncDto, userId } = params;

    this.logger.log(
      `Adding product synchronization job for TikTok Shop ID: ${syncDto.tiktokShopId} for user: ${userId}`,
    );

    // Verify that the TikTokShop exists and belongs to the user
    const shop = await this.tikTokShopRepository.findOne({
      where: {
        id: syncDto.tiktokShopId,
        userId: userId,
      },
    });

    if (!shop) {
      throw new NotFoundException(
        `TikTok Shop with ID ${syncDto.tiktokShopId} not found or access denied`,
      );
    }

    // Add job with enhanced data including shop friendly name
    const job = await this.productQueue.add(
      'sync-products',
      {
        syncDto,
        userId,
        shopFriendlyName: shop.friendly_name || shop.name, // Use friendly name or fallback to name
      },
      {
        attempts: 3, // Number of retry attempts if job fails
        backoff: {
          type: 'exponential',
          delay: 5000, // 5 seconds initial delay
        },
        removeOnComplete: false, // Keep completed jobs in the queue for monitoring
        removeOnFail: false, // Keep failed jobs in the queue for debugging
      },
    );

    this.logger.log(
      `Product synchronization job added with ID: ${job.id} for shop: ${shop.friendly_name || shop.name} (ID: ${shop.id})`,
    );

    return {
      jobId: job.id,
      tiktokShopId: shop.id,
      shopName: shop.friendly_name || shop.name,
      status: 'queued',
    };
  }

  /**
   * Add multiple product synchronization jobs to the queue (one per shop)
   * @param params Object containing syncDto and userId
   * @returns Array of job information
   */
  async addProductSyncMultiShopJobs(params: {
    syncDto: ProductSyncMultiShopDto;
    userId: number;
  }): Promise<Array<{
    jobId: string | number;
    tiktokShopId: number;
    shopName: string;
    status: string;
  }>> {
    const { syncDto, userId } = params;

    this.logger.log(
      `Adding product synchronization jobs for ${syncDto.tiktokShopIds.length} TikTok Shops for user: ${userId}`,
    );

    // Verify that all TikTokShops exist and belong to the user
    const shops = await this.tikTokShopRepository.find({
      where: {
        id: In(syncDto.tiktokShopIds),
        userId: userId,
      },
    });

    if (shops.length !== syncDto.tiktokShopIds.length) {
      const foundShopIds = shops.map(shop => shop.id);
      const missingShopIds = syncDto.tiktokShopIds.filter(id => !foundShopIds.includes(id));
      throw new NotFoundException(
        `TikTok Shops with IDs [${missingShopIds.join(', ')}] not found or access denied`,
      );
    }

    const jobResults = [];

    for (const shop of shops) {
      // Create individual ProductSyncDto for each shop
      const individualSyncDto: ProductSyncDto = {
        tiktokShopId: shop.id,
        pageSize: syncDto.pageSize,
        sortOrder: syncDto.sortOrder,
        sortField: syncDto.sortField,
        maxPages: syncDto.maxPages,
        forceFullSync: syncDto.forceFullSync,
      };

      // Add job with enhanced data including shop friendly name
      const job = await this.productQueue.add(
        'sync-products',
        {
          syncDto: individualSyncDto,
          userId,
          shopFriendlyName: shop.friendly_name || shop.name, // Use friendly name or fallback to name
        },
        {
          attempts: 3,
          backoff: {
            type: 'exponential',
            delay: 5000,
          },
          removeOnComplete: false,
          removeOnFail: false,
        },
      );

      jobResults.push({
        jobId: job.id,
        tiktokShopId: shop.id,
        shopName: shop.friendly_name || shop.name,
        status: 'queued',
      });

      this.logger.log(
        `Product synchronization job added with ID: ${job.id} for shop: ${shop.friendly_name || shop.name} (ID: ${shop.id})`,
      );
    }

    return jobResults;
  }
}
